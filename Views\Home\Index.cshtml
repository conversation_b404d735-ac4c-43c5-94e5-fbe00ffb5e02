
@{
    Layout = null;
}
<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>OWMS-OV</title>
    <meta name="Keywords" content="OWMS-OV">
    <meta name="Description" content="OWMS-OV">
    <meta name="renderer" content="webkit">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta http-equiv="Access-Control-Allow-Origin" content="*">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
    <meta name="apple-mobile-web-app-status-bar-style" content="black">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="format-detection" content="telephone=no">
    <link rel="stylesheet" href="/layui/css/layui.css" media="all" />
    <link rel="stylesheet" href="//at.alicdn.com/t/font_tnyc012u2rlwstt9.css" media="all" />
    <link rel="stylesheet" href="/css/main.css" media="all" />
    <link rel="shortcut  icon" type="image/x-icon" href="/images/hih/透明logo.gif" media="screen" />

    <style>
        #csCode {
            font-style: normal; /* 取消斜体样式 */
            font-size: 1.5em; /* 增加字体大小 */
            font-weight: bold; /* 加粗字体 */
            color: #000; /* 设置字体颜色（黑色） */
            background-color: #ffd700; /* 设置背景色为金色 */
            padding: 5px 10px; /* 内边距 */
            border-radius: 12px; /* 边角圆润 */
            border: 2px solid #ffcc00; /* 设置边框颜色为稍暗的金色 */
        }

        .layui-nav-child.adjust-right {
            left: auto;
            right: 0px;
        }
    </style>


</head>
<body class="main_body">
    <div class="layui-layout layui-layout-admin">
        <!-- 顶部 -->
        <div class="layui-header header">
            <div class="layui-main">
                <a href="#" class="logo">OWMS-OV</a>
                <!-- 显示/隐藏菜单 -->
                <a href="javascript:;" class="iconfont hideMenu icon-menu1"></a>
                <a href="#" class="logo">Version: @ViewBag.Version</a>
                <!-- 搜索 -->
                @*  <div class="layui-form component">
                <select name="modules" lay-verify="required" lay-search="">
                <option value="">直接选择或搜索选择</option>
                </select>
                <i class="layui-icon">&#xe615;</i>
                </div> *@
                <!-- 天气信息 -->
                @* <div class="weather" pc>
                <div id="tp-weather-widget"></div>
                <script>(function (T, h, i, n, k, P, a, g, e) { g = function () { P = h.createElement(i); a = h.getElementsByTagName(i)[0]; P.src = k; P.charset = "utf-8"; P.async = 1; a.parentNode.insertBefore(P, a) }; T["ThinkPageWeatherWidgetObject"] = n; T[n] || (T[n] = function () { (T[n].q = T[n].q || []).push(arguments) }); T[n].l = +new Date(); if (T.attachEvent) { T.attachEvent("onload", g) } else { T.addEventListener("load", g, false) } }(window, document, "script", "tpwidget", "//widget.seniverse.com/widget/chameleon.js"))</script>
                <script>
                tpwidget("init", {
                "flavor": "slim",
                "location": "WX4FBXXFKE4F",
                "geolocation": "enabled",
                "language": "zh-chs",
                "unit": "c",
                "theme": "chameleon",
                "container": "tp-weather-widget",
                "bubble": "disabled",
                "alarmType": "badge",
                "color": "#FFFFFF",
                "uid": "U9EC08A15F",
                "hash": "039da28f5581f4bcb5c799fb4cdfb673"
                });
                tpwidget("show");</script>
                </div> *@
                <!-- 顶部右侧菜单 -->
                <ul class="layui-nav top_menu">
                    @*  <li class="layui-nav-item showNotice" id="showNotice" pc>
                    <a href="javascript:;"><i class="iconfont icon-gonggao"></i><cite>System Announcement</cite></a>
                    </li> *@
                    <li class="layui-nav-item" mobile>
                        <a href="javascript:;" class="mobileAddTab" data-url="/UserManager/ChangePassword">
                            <i class="iconfont icon-shezhi1" data-icon="icon-shezhi1"></i><cite>设置</cite>
                        </a>
                    </li>
                    <li class="layui-nav-item" mobile>
                        <a href="/Login/Logout" class="signOut"><i class="iconfont icon-loginout"></i> 退出</a>
                    </li>
                    <li class="layui-nav-item" pc>
                        @* <a href="javascript:;"><i class="iconfont icon-lock1"></i><cite>锁屏</cite></a> *@
                        @* <label>Customer Code: <cite style="font-size: 1.3em;" id="csCode"></cite></label> *@
                    </li>
                    <li class="layui-nav-item" pc>
                        <a href="javascript:;">
                            @*  <img src="/images/face.jpg" class="layui-circle" width="35" height="35"> *@
                            <cite id="usernametop"></cite>
                        </a>
                        <dl class="layui-nav-child adjust-right">
                            @* <dd><a href="javascript:;" data-url="/UserManage/ChangePassword"><i class="iconfont icon-shezhi1" data-icon="icon-shezhi1"></i><cite>Change Password</cite></a></dd> *@
                            <dd>
                                <a href="javascript:;" class="changePasswordLink">
                                    <i class="iconfont icon-shezhi1" data-icon="icon-shezhi1"></i>
                                    <cite>Change Password</cite>
                                </a>
                            </dd>
                            @* <dd><a href="javascript:;" class="changeSkin"><i class="iconfont icon-huanfu"></i><cite>更换皮肤</cite></a></dd> *@
                            <dd><a href="/Login/Logout" class="signOut"><i class="iconfont icon-loginout"></i><cite>Login Out</cite></a></dd>
                        </dl>
                    </li>
                </ul>
            </div>
        </div>
        
        <!-- 左侧导航 -->
        <div class="layui-side layui-bg-black">
            @* 注释头像 *@
            @* <div class="user-photo">
            <a class="img" title="我的头像"><img src="/images/face.jpg"></a>
            <p>你好！<span class="userName" id="username"></span></p>
            </div> *@
            <div class="navBar"></div>
        </div>
        <!-- 右侧内容 -->
        <div class="layui-body layui-form">
            <div class="layui-tab layui-tab-card marg0" lay-filter="bodyTab" id="top_tabs_box">
                <ul class="layui-tab-title top_tab" id="top_tabs">
                    <li class="layui-this" lay-id=""><i class="iconfont icon-computer"></i> <cite>首页</cite></li>
                </ul>
                <ul class="layui-nav closeBox">
                    <li class="layui-nav-item">
                        <a href="javascript:;"><i class="iconfont icon-caozuo"></i> home</a>
                        <dl class="layui-nav-child">
                            <dd><a href="javascript:;" class="refresh refreshThis"><i class="layui-icon">&#x1002;</i> Refresh Current</a></dd>
                            <dd><a href="javascript:;" class="closePageOther"><i class="iconfont icon-prohibit"></i> Close Other</a></dd>
                            <dd><a href="javascript:;" class="closePageAll"><i class="iconfont icon-guanbi"></i> Close All</a></dd>
                        </dl>
                    </li>
                </ul>
                <div class="layui-tab-content clildFrame">
                    <div class="layui-tab-item layui-show" ng-app="myApp">
                        <iframe src="/Home/Main"></iframe>
                    </div>
                </div>
            </div>
        </div>
        <!-- 底部 -->
        @* <div class="layui-footer footer">
        <p>©版权所有 @@2024 - OWMS-OV 版权所有®
        </div> *@

    </div>

    <!-- 移动导航 -->
    <div class="site-tree-mobile layui-hide"><i class="layui-icon">&#xe602;</i></div>
    <div class="site-mobile-shade"></div>

    <script type="text/javascript" src="/layui/layui.js"></script>
    <script type="text/javascript" src="/js/leftNav.js?v2.8.11"></script>
    <script type="text/javascript" src="/js/index.js?v2.0.0"></script>
</body>
</html>